require 'rails_helper'
require 'swagger_helper'

RSpec.describe 'DELETE /api/v1/dealerships/:dealership_uuid/drives/:uuid', type: :request do
  include_context "drive_api_shared_context"

  let!(:drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, status: :completed) }

  # Regular RSpec tests
  describe 'DELETE /api/v1/dealerships/:dealership_uuid/drives/:uuid' do
    subject { delete "/api/v1/dealerships/#{dealership.uuid}/drives/#{drive.uuid}", headers: headers }

    context 'with valid authentication' do
      it 'marks the drive as deleted' do
        expect { subject }.to change { drive.reload.status }.from('completed').to('deleted')

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig('status', 'code')).to eq(200)
        expect(json.dig('status', 'message')).to eq('Drive deleted successfully')
      end

      it 'does not physically delete the record' do
        expect { subject }.not_to change { Drive.unscoped.count }
      end

      it 'marks the drive status as deleted' do
        subject
        drive.reload
        expect(drive.status).to eq('deleted')
      end

      context 'with different drive statuses' do
        let!(:draft_drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, status: :draft) }
        let!(:scheduled_drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, status: :scheduled) }
        let!(:in_progress_drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, status: :in_progress) }
        let!(:cancelled_drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, status: :cancelled) }

        it 'can delete a draft drive' do
          delete "/api/v1/dealerships/#{dealership.uuid}/drives/#{draft_drive.uuid}", headers: headers
          expect(response).to have_http_status(:ok)
          expect(draft_drive.reload.status).to eq('deleted')
        end

        it 'can delete a scheduled drive' do
          delete "/api/v1/dealerships/#{dealership.uuid}/drives/#{scheduled_drive.uuid}", headers: headers
          expect(response).to have_http_status(:ok)
          expect(scheduled_drive.reload.status).to eq('deleted')
        end

        it 'can delete an in-progress drive' do
          delete "/api/v1/dealerships/#{dealership.uuid}/drives/#{in_progress_drive.uuid}", headers: headers
          expect(response).to have_http_status(:ok)
          expect(in_progress_drive.reload.status).to eq('deleted')
        end

        it 'can delete a cancelled drive' do
          delete "/api/v1/dealerships/#{dealership.uuid}/drives/#{cancelled_drive.uuid}", headers: headers
          expect(response).to have_http_status(:ok)
          expect(cancelled_drive.reload.status).to eq('deleted')
        end
      end
    end

    context 'with non-existent drive' do
      subject { delete "/api/v1/dealerships/#{dealership.uuid}/drives/non-existent-uuid", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Drive not found')
      end
    end

    context 'with already deleted drive' do
      before { drive.update!(status: :deleted) }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Drive not found')
      end
    end

    context 'with invalid dealership' do
      subject { delete "/api/v1/dealerships/invalid-uuid/drives/#{drive.uuid}", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'without authentication' do
      let(:headers) { {} }

      it 'returns unauthorized' do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger Documentation
  path '/api/v1/dealerships/{dealership_uuid}/drives/{uuid}' do
    delete 'Delete a drive' do
      tags 'Drives'
      description 'Marks a drive as deleted (soft delete). The drive will no longer be accessible via normal queries but remains in the database for audit purposes.'
      operationId 'deleteDrive'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
      parameter name: 'uuid', in: :path, type: :string, description: 'Drive UUID'
      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device identifier'

      response '200', 'Drive deleted successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Drive deleted successfully' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        let(:uuid) { drive.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(200)
          expect(data['status']['message']).to eq('Drive deleted successfully')

          # Verify the drive is marked as deleted
          drive.reload
          expect(drive.status).to eq('deleted')
        end
      end

      response '404', 'Drive not found' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Drive not found' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        let(:uuid) { 'non-existent-uuid' }

        run_test!
      end

      response '401', 'Unauthorized' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        let(:uuid) { drive.uuid }
        let(:Authorization) { nil }
        let(:'Device-ID') { nil }

        run_test!
      end
    end
  end
end
