require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::AppraisalValuersController", type: :request do
  include_context "dealership_api_shared_context"

  let!(:active_valuer1) { create(:appraisal_valuer, dealership: dealership, business_name: "ABC Valuers", status: :active) }
  let!(:active_valuer2) { create(:appraisal_valuer, dealership: dealership, business_name: "XYZ Appraisals", status: :active) }
  let!(:inactive_valuer) { create(:appraisal_valuer, dealership: dealership, business_name: "Inactive Valuers", status: :inactive) }
  let!(:deleted_valuer) { create(:appraisal_valuer, dealership: dealership, business_name: "Deleted Valuers", status: :deleted) }
  let!(:other_dealership_valuer) { create(:appraisal_valuer, business_name: "Other Dealership Valuer", status: :active) }

  path "/api/v1/dealerships/{dealership_uuid}/valuers" do
    get "Get appraisal valuers for a dealership" do
      tags "Appraisal Valuers"
      produces "application/json"
      security [ Bearer: [] ]
      description "Retrieves all active appraisal valuers for a specific dealership with pagination support"

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token for authentication"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device ID for authentication"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "UUID of the dealership"
      parameter name: :page, in: :query, type: :integer, required: false, description: "Page number for pagination (default: 1)"
      parameter name: :per_page, in: :query, type: :integer, required: false, description: "Number of items per page (default: 20, max: 100)"

      response "200", "Appraisal valuers retrieved successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Appraisal valuers retrieved successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     valuers: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, example: "123e4567-e89b-12d3-a456-************" },
                           business_name: { type: :string, example: "ABC Auto Valuers" },
                           email: { type: :string, example: "<EMAIL>" },
                           first_name: { type: :string, example: "John" },
                           last_name: { type: :string, example: "Doe" },
                           mobile_number: { type: :string, example: "+***********" },
                           status: { type: :string, example: "active" },
                           full_name: { type: :string, example: "John Doe" },
                           created_at: { type: :string, format: :datetime, example: "2024-01-01T00:00:00.000Z" },
                           updated_at: { type: :string, format: :datetime, example: "2024-01-01T00:00:00.000Z" }
                         }
                       }
                     }
                   }
                 }
               }

        let(:dealership_uuid) { dealership.uuid }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Appraisal valuers retrieved successfully")

          valuers = json.dig("data", "valuers")
          expect(valuers.length).to eq(2)

          expect(valuers.first["business_name"]).to eq("ABC Valuers")
          expect(valuers.second["business_name"]).to eq("XYZ Appraisals")

          first_valuer = valuers.first
          expect(first_valuer).to have_key("uuid")
          expect(first_valuer).to have_key("business_name")
          expect(first_valuer).to have_key("email")
          expect(first_valuer).to have_key("first_name")
          expect(first_valuer).to have_key("last_name")
          expect(first_valuer).to have_key("mobile_number")
          expect(first_valuer).to have_key("status")
          expect(first_valuer).to have_key("full_name")
          expect(first_valuer).to have_key("created_at")
          expect(first_valuer).to have_key("updated_at")

          expect(first_valuer["status"]).to eq("active")
          expect(first_valuer["full_name"]).to eq("#{active_valuer1.first_name} #{active_valuer1.last_name}")

          expect(response.headers["X-Current-Page"]).to eq("1")
          expect(response.headers["X-Per-Page"]).to eq("20")
          expect(response.headers["X-Total-Count"]).to eq("2")
          expect(response.headers["X-Total-Pages"]).to eq("1")
        end
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Unauthorized" }
                   }
                 }
               }

        let(:dealership_uuid) { dealership.uuid }
        let(:Authorization) { "Bearer invalid_token" }

        run_test!
      end

      response "404", "Dealership not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Dealership not found or you don't have access to it" }
                   }
                 }
               }

        let(:dealership_uuid) { "non-existent-uuid" }

        run_test!
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/valuers" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/valuers" }

    context "with valid authentication" do
      it "returns only active valuers for the dealership" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        valuers = json.dig("data", "valuers")
        expect(valuers.length).to eq(2)

        valuer_uuids = valuers.map { |v| v["uuid"] }
        expect(valuer_uuids).to include(active_valuer1.uuid, active_valuer2.uuid)
        expect(valuer_uuids).not_to include(inactive_valuer.uuid, deleted_valuer.uuid, other_dealership_valuer.uuid)
      end

      it "orders valuers by business_name" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        valuers = json.dig("data", "valuers")
        business_names = valuers.map { |v| v["business_name"] }
        expect(business_names).to eq([ "ABC Valuers", "XYZ Appraisals" ])
      end

      it "includes all required fields in the response" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        valuer = json.dig("data", "valuers").first
        expected_fields = %w[uuid business_name email first_name last_name mobile_number status full_name created_at updated_at]

        expected_fields.each do |field|
          expect(valuer).to have_key(field), "Expected field '#{field}' to be present"
        end
      end

      it "sets correct pagination headers" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        expect(response.headers["X-Current-Page"]).to eq("1")
        expect(response.headers["X-Per-Page"]).to eq("20")
        expect(response.headers["X-Total-Count"]).to eq("2")
        expect(response.headers["X-Total-Pages"]).to eq("1")
      end

      context "with pagination parameters" do
        it "respects per_page parameter" do
          get url, params: { per_page: 1 }, headers: headers

          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          valuers = json.dig("data", "valuers")
          expect(valuers.length).to eq(1)
          expect(response.headers["X-Per-Page"]).to eq("1")
          expect(response.headers["X-Total-Pages"]).to eq("2")
        end

        it "respects page parameter" do
          get url, params: { per_page: 1, page: 2 }, headers: headers

          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          valuers = json.dig("data", "valuers")
          expect(valuers.length).to eq(1)
          expect(valuers.first["business_name"]).to eq("XYZ Appraisals")
          expect(response.headers["X-Current-Page"]).to eq("2")
        end
      end
    end

    context "with invalid authentication" do
      it "returns 401 for invalid token" do
        get url, headers: { "Authorization" => "Bearer invalid_token", "Device-ID" => device_registration.device_id }

        expect(response).to have_http_status(:unauthorized)
      end

      it "returns 401 for missing Device-ID" do
        get url, headers: { "Authorization" => "Bearer #{valid_token}" }

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with invalid dealership" do
      it "returns 404 for non-existent dealership" do
        get "/api/v1/dealerships/non-existent-uuid/valuers", headers: headers

        expect(response).to have_http_status(:not_found)
      end

      it "returns 404 for dealership user doesn't have access to" do
        other_dealership = create(:dealership)
        get "/api/v1/dealerships/#{other_dealership.uuid}/valuers", headers: headers

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
