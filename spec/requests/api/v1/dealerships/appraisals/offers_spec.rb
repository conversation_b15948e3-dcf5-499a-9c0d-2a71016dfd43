# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "GET /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/offers", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :complete) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }
  let(:appraisal_uuid) { appraisal.uuid }
  let(:valuer) { create(:appraisal_valuer, dealership: dealership) }
  let(:internal_user) { create(:user) }
  let!(:internal_user_dealership) { create(:user_dealership, user: internal_user, dealership: dealership, role: :sales_person) }

  # Create test offers
  let!(:external_offer) do
    create(:appraisal_offer,
           appraisal: appraisal,
           appraisal_valuer: valuer,
           offer_price: 25000.00,
           offer_notes: "External valuer offer",
           offer_date: Date.current,
           is_verbal_offer: false,
           is_internal_offer: false,
           awarded: false)
  end

  let!(:internal_offer) do
    create(:appraisal_offer,
           appraisal: appraisal,
           appraisal_valuer: nil,
           verbal_offer_by: internal_user,
           offer_price: 27000.00,
           offer_notes: "Internal verbal offer",
           offer_date: Date.current,
           is_verbal_offer: true,
           is_internal_offer: true,
           awarded: true,
           awarded_at: Time.current,
           valuer_first_name: nil,
           valuer_last_name: nil,
           valuer_email: nil,
           valuer_business_name: nil,
           valuer_mobile_number: nil)
  end

  let!(:direct_valuer_offer) do
    create(:appraisal_offer,
           appraisal: appraisal,
           appraisal_valuer: nil,
           valuer_first_name: "John",
           valuer_last_name: "Doe",
           valuer_email: "<EMAIL>",
           valuer_business_name: "Doe Valuations",
           valuer_mobile_number: "+***********",
           offer_price: 24000.00,
           offer_notes: "Direct valuer offer",
           offer_date: Date.current,
           is_verbal_offer: true,
           is_internal_offer: false,
           awarded: false)
  end

  # Regular RSpec tests
  describe 'GET /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/offers' do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/offers", headers: headers }

    context 'with valid authentication' do
      it 'returns all offers for the appraisal' do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to eq('Offers retrieved successfully')

        offers_data = json.dig('data', 'offers')
        expect(offers_data).to be_an(Array)
        expect(offers_data.length).to eq(3)
      end

      it 'returns correct data for external valuer offer' do
        subject
        json = response.parsed_body
        offers_data = json.dig('data', 'offers')

        external_offer_data = offers_data.find { |offer| offer['uuid'] == external_offer.uuid }
        expect(external_offer_data).to be_present
        expect(external_offer_data['valuer_name']).to eq(valuer.full_name)
        expect(external_offer_data['valuer_business_name']).to eq(valuer.business_name)
        expect(external_offer_data['valuer_email']).to eq(valuer.email)
        expect(external_offer_data['valuer_mobile_number']).to eq(valuer.mobile_number)
        expect(external_offer_data['offer_price']).to eq("25000.0")
        expect(external_offer_data['offer_notes']).to eq("External valuer offer")
        expect(external_offer_data['valuer_uuid']).to eq(valuer.uuid)
        expect(external_offer_data['is_verbal_offer']).to be false
        expect(external_offer_data['is_awarded']).to be false
        expect(external_offer_data['is_internal_offer']).to be false
      end

      it 'returns correct data for internal offer' do
        subject
        json = response.parsed_body
        offers_data = json.dig('data', 'offers')

        internal_offer_data = offers_data.find { |offer| offer['uuid'] == internal_offer.uuid }
        expect(internal_offer_data).to be_present
        expect(internal_offer_data['valuer_name']).to eq(internal_user.full_name)
        expect(internal_offer_data['valuer_business_name']).to eq("Internal")
        expect(internal_offer_data['valuer_email']).to eq(internal_user.email)
        expect(internal_offer_data['valuer_mobile_number']).to eq(internal_user.phone)
        expect(internal_offer_data['offer_price']).to eq("27000.0")
        expect(internal_offer_data['offer_notes']).to eq("Internal verbal offer")
        expect(internal_offer_data['valuer_uuid']).to be_nil
        expect(internal_offer_data['is_verbal_offer']).to be true
        expect(internal_offer_data['verbal_offer_from_user']).to eq(internal_user.full_name)
        expect(internal_offer_data['is_awarded']).to be true
        expect(internal_offer_data['awarded_at']).to be_present
        expect(internal_offer_data['is_internal_offer']).to be true
      end

      it 'returns correct data for direct valuer offer' do
        subject
        json = response.parsed_body
        offers_data = json.dig('data', 'offers')

        direct_offer_data = offers_data.find { |offer| offer['uuid'] == direct_valuer_offer.uuid }
        expect(direct_offer_data).to be_present
        expect(direct_offer_data['valuer_name']).to eq("John Doe")
        expect(direct_offer_data['valuer_business_name']).to eq("Doe Valuations")
        expect(direct_offer_data['valuer_email']).to eq("<EMAIL>")
        expect(direct_offer_data['valuer_mobile_number']).to eq("+***********")
        expect(direct_offer_data['offer_price']).to eq("24000.0")
        expect(direct_offer_data['offer_notes']).to eq("Direct valuer offer")
        expect(direct_offer_data['valuer_uuid']).to be_nil
        expect(direct_offer_data['is_verbal_offer']).to be true
        expect(direct_offer_data['is_awarded']).to be false
        expect(direct_offer_data['is_internal_offer']).to be false
      end
    end

    context 'with invalid appraisal UUID' do
      it 'returns 404 not found' do
        get "/api/v1/dealerships/#{dealership.uuid}/appraisals/ffffffff-ffff-ffff-ffff-ffffffffffff/offers", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'without authentication' do
      let(:headers) { {} }

      it 'returns 401 unauthorized' do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger API Documentation
  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/offers" do
    get "Get offers for an appraisal" do
      tags "Appraisals"
      produces "application/json"
      security [ Bearer: [] ]
      description "Retrieve all offers for a specific appraisal with detailed valuer information"

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      response "200", "Offers retrieved successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Offers retrieved successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     offers: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, format: :uuid, example: "550e8400-e29b-41d4-a716-************" },
                           valuer_name: { type: :string, example: "John Smith" },
                           valuer_business_name: { type: :string, example: "Smith Valuations" },
                           valuer_email: { type: :string, format: :email, example: "<EMAIL>" },
                           valuer_mobile_number: { type: :string, example: "+***********" },
                           offer_date: { type: :string, format: :date, example: "2024-01-15" },
                           offer_price: { type: :string, example: "25000.0", description: "Decimal value as string" },
                           offer_notes: { type: :string, example: "Vehicle in excellent condition" },
                           valuer_uuid: { type: :string, format: :uuid, example: "550e8400-e29b-41d4-a716-************", nullable: true },
                           is_verbal_offer: { type: :boolean, example: false },
                           verbal_offer_from_user: { type: :string, example: "Jane Doe", nullable: true },
                           is_awarded: { type: :boolean, example: false },
                           awarded_at: { type: :string, format: :datetime, example: "2024-01-15T10:30:00Z", nullable: true },
                           is_internal_offer: { type: :boolean, example: false }
                         }
                       }
                     }
                   }
                 }
               }

        run_test!
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 error: { type: :string, example: "Unauthorized" }
               }

        let(:Authorization) { nil }
        run_test!
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 error: { type: :string, example: "Not Found" }
               }

        let(:appraisal_uuid) { "ffffffff-ffff-ffff-ffff-ffffffffffff" }
        run_test!
      end
    end
  end
end
