# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-history", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }

  # Default vehicle_history for test cases that don't define their own
  let(:vehicle_history) { {} }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-history" do
    put "Upsert vehicle history for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :vehicle_history, in: :formData, schema: {
        type: :object,
        properties: {
          number_of_owners: { type: :integer, description: "Number of previous owners", example: 2 },
          has_accident_history: { type: :boolean, description: "Whether the vehicle has been in accidents", example: false },
          accident_details: { type: :string, description: "Details about any accidents", example: "Minor rear-end collision in 2022" },
          last_service_date: { type: :string, format: :date, description: "Date of last service", example: "2024-01-15" },
          last_service_odometer: { type: :integer, description: "Odometer reading at last service", example: 45000 },
          next_service_due: { type: :string, description: "Date when next service is due", example: "2024-07-15" },
          has_dash_warning_lights: { type: :boolean, description: "Whether there are warning lights on the dashboard", example: false },
          dash_warning_details: { type: :string, description: "Details about dashboard warning lights", example: "Check engine light on" },
          notes: { type: :string, description: "Additional notes about vehicle history", example: "Well maintained vehicle with full service history" },
          vehicle_history_status: { type: :string, enum: %w[no_history partial_history full_mixed_history full_oem_history], description: "Status of vehicle history documentation", example: "full_oem_history" },
          service_book_images: { type: :array, items: { type: :string, format: :binary }, description: "Images of service book pages", maxItems: 5 }
        }
      }

      response "201", "Vehicle history created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Vehicle history created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_history: {
                               type: :object,
                               properties: {
                                 number_of_owners: { type: :integer, example: 2 },
                                 has_accident_history: { type: :boolean, example: false },
                                 accident_details: { type: :string, example: "Minor rear-end collision in 2022" },
                                 last_service_date: { type: :string, format: :date, example: "2024-01-15" },
                                 last_service_odometer: { type: :integer, example: 45000 },
                                 next_service_due: { type: :string, format: :date, example: "2024-07-15" },
                                 has_dash_warning_lights: { type: :boolean, example: false },
                                 dash_warning_details: { type: :string, example: "Check engine light occasionally appears" },
                                 notes: { type: :string, example: "Well maintained vehicle with full service history" },
                                 vehicle_history_status: { type: :string, example: "full_oem_history" },
                                 service_book_images: { type: :array, items: { type: :object, properties: { id: { type: :integer }, url: { type: :string } } } }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:service_book_image1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/service_book_page1.jpg"), 'image/jpeg') }
        let(:vehicle_history) do
          {
            number_of_owners: 2,
            has_accident_history: false,
            accident_details: "Minor rear-end collision in 2022",
            last_service_date: "2024-01-15",
            last_service_odometer: 45000,
            next_service_due: "2024-07-15",
            has_dash_warning_lights: false,
            dash_warning_details: "Check engine light occasionally appears",
            notes: "Well maintained vehicle with full service history",
            vehicle_history_status: "full_oem_history",
            service_book_images: [ service_book_image1 ]
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Vehicle history created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          vehicle_history = json.dig("data", "appraisal", "vehicle", "vehicle_history")
          expect(vehicle_history).to be_present
          expect(vehicle_history["number_of_owners"]).to eq(2)
          expect(vehicle_history["has_accident_history"]).to eq(false)
          expect(vehicle_history["accident_details"]).to eq("Minor rear-end collision in 2022")
          expect(vehicle_history["last_service_date"]).to eq("2024-01-15")
          expect(vehicle_history["last_service_odometer"]).to eq(45000)
          expect(vehicle_history["next_service_due"]).to eq("2024-07-15")
          expect(vehicle_history["has_dash_warning_lights"]).to eq(false)
          expect(vehicle_history["dash_warning_details"]).to eq("Check engine light occasionally appears")
          expect(vehicle_history["notes"]).to eq("Well maintained vehicle with full service history")
          expect(vehicle_history["vehicle_history_status"]).to eq("full_oem_history")

          expect(vehicle_history["service_book_images"]).to be_an(Array)
        end
      end

      response "200", "Vehicle history updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Vehicle history updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_history: {
                               type: :object,
                               properties: {
                                 number_of_owners: { type: :integer, example: 3 },
                                 has_accident_history: { type: :boolean, example: true },
                                 accident_details: { type: :string, example: "Major front-end collision in 2023" },
                                 last_service_date: { type: :string, format: :date, example: "2024-02-15" },
                                 last_service_odometer: { type: :integer, example: 52000 },
                                 next_service_due: { type: :string, format: :date, example: "2024-08-15" },
                                 has_dash_warning_lights: { type: :boolean, example: true },
                                 dash_warning_details: { type: :string, example: "ABS warning light on" },
                                 notes: { type: :string, example: "Vehicle has been repaired after accident" },
                                 vehicle_history_status: { type: :string, example: "partial_history" },
                                 service_book_images: { type: :array, items: { type: :object, properties: { id: { type: :integer }, url: { type: :string } } } }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_history) { create(:vehicle_history, customer_vehicle: customer_vehicle, number_of_owners: 2, has_accident_history: false, last_service_date: "2024-01-15") }
        let(:service_book_image1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/service_book_page1.jpg"), 'image/jpeg') }
        let(:vehicle_history) do
          {
            number_of_owners: 3,
            has_accident_history: true,
            accident_details: "Major front-end collision in 2023",
            last_service_date: "2024-02-15",
            last_service_odometer: 52000,
            next_service_due: "2024-08-15",
            has_dash_warning_lights: true,
            dash_warning_details: "ABS warning light on",
            notes: "Vehicle has been repaired after accident",
            vehicle_history_status: "partial_history",
            service_book_images: [ service_book_image1 ]
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Vehicle history updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          vehicle_history = json.dig("data", "appraisal", "vehicle", "vehicle_history")
          expect(vehicle_history).to be_present
          expect(vehicle_history["number_of_owners"]).to eq(3)
          expect(vehicle_history["has_accident_history"]).to eq(true)
          expect(vehicle_history["accident_details"]).to eq("Major front-end collision in 2023")
          expect(vehicle_history["last_service_date"]).to eq("2024-02-15")
          expect(vehicle_history["last_service_odometer"]).to eq(52000)
          expect(vehicle_history["next_service_due"]).to eq("2024-08-15")
          expect(vehicle_history["has_dash_warning_lights"]).to eq(true)
          expect(vehicle_history["dash_warning_details"]).to eq("ABS warning light on")
          expect(vehicle_history["notes"]).to eq("Vehicle has been repaired after accident")
          expect(vehicle_history["vehicle_history_status"]).to eq("partial_history")

          expect(vehicle_history["service_book_images"]).to be_an(Array)
        end
      end

      response "422", "Validation failed" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Number of owners must be an integer" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle_history) { { number_of_owners: "invalid" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
        end
      end

      response "422", "No vehicle found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal_without_vehicle.uuid }
        let(:appraisal_without_vehicle) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let(:vehicle_history) { { number_of_owners: 2, has_accident_history: false } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let(:vehicle_history) { { number_of_owners: 2, has_accident_history: false } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:vehicle_history) { { number_of_owners: 2, has_accident_history: false } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle_history) { { number_of_owners: 2, has_accident_history: false } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      # Note: File upload validation testing is limited in rswag due to nested parameter structure.
      # The vehicle history model validates service_book_images content type and size in real usage.
    end
  end
end
