# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/options-fitted", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }

  # Default options_fitted for test cases that don't define their own
  let(:options_fitted) { {} }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/options-fitted" do
    put "Upsert options fitted for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :options_fitted, in: :formData, schema: {
        type: :object,
        properties: {
          has_sunroof: { type: :boolean, description: "Whether the vehicle has a sunroof", example: true },
          has_tinted_windows: { type: :boolean, description: "Whether the vehicle has tinted windows", example: true },
          has_towbar: { type: :boolean, description: "Whether the vehicle has a towbar", example: false },
          has_keyless_entry: { type: :boolean, description: "Whether the vehicle has keyless entry", example: true },
          has_bluetooth: { type: :boolean, description: "Whether the vehicle has bluetooth", example: true },
          has_ventilated_seats: { type: :boolean, description: "Whether the vehicle has ventilated seats", example: false },
          has_tray_fitted: { type: :boolean, description: "Whether the vehicle has a tray fitted", example: false },
          has_canopy_fitted: { type: :boolean, description: "Whether the vehicle has a canopy fitted", example: false },
          has_aftermarket_wheels: { type: :boolean, description: "Whether the vehicle has aftermarket wheels", example: false },
          has_bull_bar: { type: :boolean, description: "Whether the vehicle has a bull bar", example: false },
          has_extended_warranty: { type: :boolean, description: "Whether the vehicle has extended warranty", example: true },
          extended_warranty_expiry: { type: :string, format: :date, description: "Extended warranty expiry date", example: "2025-12-31" },
          ppsr: { type: :boolean, description: "Whether the vehicle has PPSR", example: false },
          additional_options: { type: :object, description: "Additional options as JSON object", example: { "premium_sound_system" => true, "navigation" => true } },
          sunroof_type: { type: :string, enum: %w[standard_metal standard_glass panoramic], description: "Type of sunroof if present", example: "panoramic" },
          number_of_keys: { type: :integer, enum: [ 1, 2, 3 ], description: "Number of keys provided", example: 2 },
          heated_seats: { type: :boolean, description: "Whether the vehicle has heated seats", example: true },
          cargo_blind: { type: :boolean, description: "Whether the vehicle has a cargo blind", example: false },
          tonneau_cover: { type: :boolean, description: "Whether the vehicle has a tonneau cover", example: false },
          tonneau_type: { type: :string, enum: %w[hard soft], description: "Type of tonneau cover if present", example: "hard" },
          on_written_off_register: { type: :string, enum: %w[yes no unknown], description: "Whether the vehicle is on written off register", example: "no" },
          last_ppsr_date: { type: :string, format: :date, description: "Last PPSR check date", example: "2024-01-15" },
          notes: { type: :string, description: "Additional notes about vehicle options", example: "Vehicle has premium sound system and custom modifications" },
          options_images: { type: :array, items: { type: :string, format: :binary }, description: "Images of vehicle options", maxItems: 5 }
        }
      }

      response "201", "Options fitted created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Options fitted created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             options_fitted: {
                               type: :object,
                               properties: {
                                 has_sunroof: { type: :boolean, example: true },
                                 has_tinted_windows: { type: :boolean, example: true },
                                 has_towbar: { type: :boolean, example: false },
                                 has_keyless_entry: { type: :boolean, example: true },
                                 has_bluetooth: { type: :boolean, example: true },
                                 has_extended_warranty: { type: :boolean, example: true },
                                 extended_warranty_expiry: { type: :string, format: :date, example: "2025-12-31" },
                                 sunroof_type: { type: :string, example: "panoramic" },
                                 number_of_keys: { type: :integer, example: 2 },
                                 heated_seats: { type: :boolean, example: true },
                                 on_written_off_register: { type: :string, example: "no" },
                                 notes: { type: [ :string, :null ], example: "Vehicle has premium sound system and custom modifications" },
                                 options_images: { type: :array, items: { type: :object, properties: { id: { type: :integer }, url: { type: :string } } } }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:options_image1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:options_fitted) do
          {
            has_sunroof: true,
            has_tinted_windows: true,
            has_towbar: false,
            has_keyless_entry: true,
            has_bluetooth: true,
            has_ventilated_seats: false,
            has_tray_fitted: false,
            has_canopy_fitted: false,
            has_aftermarket_wheels: false,
            has_bull_bar: false,
            has_extended_warranty: true,
            extended_warranty_expiry: "2025-12-31",
            ppsr: false,
            additional_options: { "premium_sound_system" => true, "navigation" => true },
            sunroof_type: "panoramic",
            number_of_keys: 2,
            heated_seats: true,
            cargo_blind: false,
            tonneau_cover: false,
            tonneau_type: "hard",
            on_written_off_register: "no",
            last_ppsr_date: "2024-01-15",
            options_images: [ options_image1 ]
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Options fitted created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          options_fitted = json.dig("data", "appraisal", "vehicle", "options_fitted")
          expect(options_fitted).to be_present
          expect(options_fitted["has_sunroof"]).to eq(true)
          expect(options_fitted["has_tinted_windows"]).to eq(true)
          expect(options_fitted["has_towbar"]).to eq(false)
          expect(options_fitted["has_keyless_entry"]).to eq(true)
          expect(options_fitted["has_bluetooth"]).to eq(true)
          expect(options_fitted["has_extended_warranty"]).to eq(true)
          expect(options_fitted["extended_warranty_expiry"]).to eq("2025-12-31")
          expect(options_fitted["sunroof_type"]).to eq("panoramic")
          expect(options_fitted["number_of_keys"]).to eq(2)
          expect(options_fitted["heated_seats"]).to eq(true)
          expect(options_fitted["on_written_off_register"]).to eq("no")
          expect(options_fitted["additional_options"]).to eq({ "premium_sound_system" => "true", "navigation" => "true" })

          expect(options_fitted["options_images"]).to be_an(Array)
        end
      end

      response "200", "Options fitted updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Options fitted updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             options_fitted: {
                               type: :object,
                               properties: {
                                 has_sunroof: { type: :boolean, example: false },
                                 has_tinted_windows: { type: :boolean, example: false },
                                 has_towbar: { type: :boolean, example: true },
                                 has_keyless_entry: { type: :boolean, example: false },
                                 has_bluetooth: { type: :boolean, example: false },
                                 has_extended_warranty: { type: :boolean, example: false },
                                 sunroof_type: { type: :string, example: "standard_metal" },
                                 number_of_keys: { type: :integer, example: 1 },
                                 heated_seats: { type: :boolean, example: false },
                                 on_written_off_register: { type: :string, example: "unknown" },
                                 options_images: { type: :array, items: { type: :object, properties: { id: { type: :integer }, url: { type: :string } } } }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_options) { create(:options_fitted, customer_vehicle: customer_vehicle, has_sunroof: true, has_tinted_windows: true, has_keyless_entry: true) }
        let(:options_image1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:options_fitted) do
          {
            has_sunroof: false,
            has_tinted_windows: false,
            has_towbar: true,
            has_keyless_entry: false,
            has_bluetooth: false,
            has_ventilated_seats: false,
            has_tray_fitted: false,
            has_canopy_fitted: false,
            has_aftermarket_wheels: false,
            has_bull_bar: false,
            has_extended_warranty: false,
            extended_warranty_expiry: nil,
            ppsr: false,
            additional_options: {},
            sunroof_type: "standard_metal",
            number_of_keys: 1,
            heated_seats: false,
            cargo_blind: false,
            tonneau_cover: false,
            tonneau_type: "hard",
            on_written_off_register: "unknown",
            last_ppsr_date: nil,
            notes: "Vehicle has custom modifications and premium features",
            options_images: [ options_image1 ]
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Options fitted updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          options_fitted = json.dig("data", "appraisal", "vehicle", "options_fitted")
          expect(options_fitted).to be_present
          expect(options_fitted["has_sunroof"]).to eq(false)
          expect(options_fitted["has_tinted_windows"]).to eq(false)
          expect(options_fitted["has_towbar"]).to eq(true)
          expect(options_fitted["has_keyless_entry"]).to eq(false)
          expect(options_fitted["has_bluetooth"]).to eq(false)
          expect(options_fitted["has_extended_warranty"]).to eq(false)
          expect(options_fitted["sunroof_type"]).to eq("standard_metal")
          expect(options_fitted["number_of_keys"]).to eq(1)
          expect(options_fitted["heated_seats"]).to eq(false)
          expect(options_fitted["on_written_off_register"]).to eq("unknown")
          expect(options_fitted["notes"]).to eq("Vehicle has custom modifications and premium features")

          expect(options_fitted["options_images"]).to be_an(Array)
        end
      end

      response "422", "Validation failed" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Number of keys must be included in the list" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:options_fitted) { { number_of_keys: 5 } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
        end
      end

      response "422", "No vehicle found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal_without_vehicle.uuid }
        let(:appraisal_without_vehicle) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let(:options_fitted) { { has_sunroof: true, has_tinted_windows: true } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let(:options_fitted) { { has_sunroof: true, has_tinted_windows: true } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:options_fitted) { { has_sunroof: true, has_tinted_windows: true } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:options_fitted) { { has_sunroof: true, has_tinted_windows: true } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      # Note: File upload validation testing is limited in rswag due to nested parameter structure.
      # The options fitted model validates options_images content type and size in real usage.
    end
  end

  # Additional tests for dependent field clearing behavior
  describe "dependent field clearing behavior" do
    let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
    let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }
    let!(:existing_options) {
      create(:options_fitted, customer_vehicle: customer_vehicle,
             has_sunroof: true, sunroof_type: :panoramic,
             tonneau_cover: true, tonneau_type: :hard,
             has_extended_warranty: true, extended_warranty_expiry: 1.year.from_now.to_date,
             ppsr: true, last_ppsr_date: Date.current)
    }

    it "clears dependent fields when boolean flags are set to false" do
      put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/options-fitted",
          params: {
            options_fitted: {
              has_sunroof: false,
              tonneau_cover: false,
              has_extended_warranty: false,
              ppsr: false
            }
          },
          headers: headers

      expect(response).to have_http_status(:ok)

      json = response.parsed_body
      options_fitted = json.dig("data", "appraisal", "vehicle", "options_fitted")

      expect(options_fitted["has_sunroof"]).to eq(false)
      expect(options_fitted["tonneau_cover"]).to eq(false)
      expect(options_fitted["has_extended_warranty"]).to eq(false)
      expect(options_fitted["ppsr"]).to eq(false)

      # Verify dependent fields are cleared
      expect(options_fitted["sunroof_type"]).to be_nil
      expect(options_fitted["tonneau_type"]).to be_nil
      expect(options_fitted["extended_warranty_expiry"]).to be_nil
      expect(options_fitted["last_ppsr_date"]).to be_nil
    end

    it "allows setting dependent fields when boolean flags are true" do
      put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/options-fitted",
          params: {
            options_fitted: {
              has_sunroof: true,
              sunroof_type: "standard_glass",
              tonneau_cover: true,
              tonneau_type: "soft",
              has_extended_warranty: true,
              extended_warranty_expiry: "2026-12-31",
              ppsr: true,
              last_ppsr_date: "2024-02-15"
            }
          },
          headers: headers

      expect(response).to have_http_status(:ok)

      json = response.parsed_body
      options_fitted = json.dig("data", "appraisal", "vehicle", "options_fitted")

      expect(options_fitted["has_sunroof"]).to eq(true)
      expect(options_fitted["sunroof_type"]).to eq("standard_glass")
      expect(options_fitted["tonneau_cover"]).to eq(true)
      expect(options_fitted["tonneau_type"]).to eq("soft")
      expect(options_fitted["has_extended_warranty"]).to eq(true)
      expect(options_fitted["extended_warranty_expiry"]).to eq("2026-12-31")
      expect(options_fitted["ppsr"]).to eq(true)
      expect(options_fitted["last_ppsr_date"]).to eq("2024-02-15")
    end
  end
end
