json.status do
  json.code 200
  json.message "Offers retrieved successfully"
end

json.data do
  json.offers do
    json.array! @offers do |offer|
      json.uuid offer.uuid
      json.valuer_name offer.computed_valuer_name
      json.valuer_business_name offer.computed_valuer_business_name
      json.valuer_email offer.computed_valuer_email
      json.valuer_mobile_number offer.computed_valuer_mobile_number
      json.offer_date offer.offer_date
      json.offer_price offer.offer_price
      json.offer_notes offer.offer_notes
      json.valuer_uuid offer.appraisal_valuer&.uuid
      json.is_verbal_offer offer.is_verbal_offer
      json.verbal_offer_from_user offer.verbal_offer_by&.full_name
      json.is_awarded offer.awarded
      json.awarded_at offer.awarded_at
      json.is_internal_offer offer.is_internal_offer
    end
  end
end
