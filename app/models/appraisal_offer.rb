class AppraisalOffer < ApplicationRecord
  include HasUuid

  belongs_to :appraisal_valuer, optional: true
  belongs_to :verbal_offer_by, class_name: "User", optional: true
  belongs_to :appraisal

  validates :offer_price, numericality: { greater_than: 0 }, allow_nil: true
  validates :offer_notes, length: { maximum: 1000 }, allow_blank: true

  validates :valuer_business_name, length: { maximum: 255 }
  validates :valuer_email, email_format: true, length: { maximum: 255 }
  validates :valuer_first_name, length: { maximum: 100 }
  validates :valuer_last_name, length: { maximum: 100 }
  validates :valuer_mobile_number, length: { maximum: 20 }

  validates :valuer_first_name, :valuer_last_name, :valuer_email, :valuer_mobile_number, presence: true, unless: :skip_valuer_details_validation?

  def computed_valuer_name
    if is_internal_offer?
      verbal_offer_by&.full_name
    elsif appraisal_valuer_id.present?
      appraisal_valuer.full_name
    else
      "#{valuer_first_name} #{valuer_last_name}"
    end
  end

  def computed_valuer_email
    if is_internal_offer?
      verbal_offer_by&.email
    elsif appraisal_valuer_id.present?
      appraisal_valuer.email
    else
      valuer_email
    end
  end

  def computed_valuer_mobile_number
    if is_internal_offer?
      verbal_offer_by&.phone
    elsif appraisal_valuer_id.present?
      appraisal_valuer.mobile_number
    else
      valuer_mobile_number
    end
  end

  def computed_valuer_business_name
    if is_internal_offer?
      "Internal"
    elsif appraisal_valuer_id.present?
      appraisal_valuer.business_name
    else
      valuer_business_name
    end
  end

  private

  def skip_valuer_details_validation?
    appraisal_valuer_id? || is_internal_offer?
  end
end
