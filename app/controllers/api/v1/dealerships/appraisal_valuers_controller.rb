class Api::V1::Dealerships::AppraisalValuersController < Api::V1::BaseController
  def index
    @pagy, @appraisal_valuers = pagy(
      filtered_valuers,
      limit: pagination_validated_per_page,
      page: permitted_params[:page] || 1
    )

    set_pagination_headers(@pagy)

    render_success_response("Appraisal valuers retrieved successfully", {
      valuers: Api::V1::AppraisalValuerSerializer.render_as_json(@appraisal_valuers)
    })
  end

  private

  def filtered_valuers
    dealership.appraisal_valuers.active.order(:business_name)
  end

  def permitted_params
    params.permit(:page, :per_page)
  end
end
