Rails.application.routes.draw do
  mount Rswag::Ui::Engine => "/api-docs"
  mount Rswag::Api::Engine => "/api-docs"
  mount RailsAdmin::Engine => "/radmin", as: "rails_admin"
  require "sidekiq/web"
  mount Sidekiq::Web => "/sidekiq"

  # HTML routes (no JWT)
  devise_for :users

  # API routes (JWT)
  namespace :api, defaults: { format: :json } do
    namespace :v1 do
      namespace :auth do
        devise_scope :user do
          put "change-password", controller: "/api/v1/users", to: "change_password"
          post "forgot-password", controller: "/api/v1/users", to: "forgot_password"
          post "/login", controller: "/api/v1/sessions", to: "create"
          post "refresh", controller: "/api/v1/users", to: "refresh"
          post "verify-2fa", controller: "/api/v1/users", to: "verify_2fa"
          post "resend-otp", controller: "/api/v1/users", to: "resend_otp"
          post "verify-reset-code", controller: "/api/v1/users", to: "verify_reset_code"
          post "reset-password", controller: "/api/v1/users", to: "reset_password"
          post "setup-2fa", controller: "/api/v1/users", to: "setup_2fa"
          post "verify-2fa-setup", controller: "/api/v1/users", to: "verify_2fa_setup"
          post "request-2fa-method", controller: "/api/v1/users", to: "request_2fa_method"
        end
        delete "/logout", controller: "/api/v1/devices", to: "logout_device"
      end

      # Device management
      get "/devices", to: "devices#active_devices"
      post "/devices", to: "devices#update_device"
      patch "/devices", to: "devices#update_device"
      delete "/devices/:device_id", to: "devices#logout_device"
      delete "/devices", to: "devices#logout_all_devices"
      # Profile management
      get "profile", to: "users#profile"
      patch "profile", to: "users#update_profile"
      put "profile", to: "users#update_profile"

      namespace :profile do
        put "photo", controller: "/api/v1/users", to: "update_photo"
        delete "photo", controller: "/api/v1/users", to: "destroy_photo"
      end

      # Dealerships routes
      resources :dealerships, only: [ :index ], param: :uuid do
        member do
          get "agreements"
        end
        # Customer management
        resources :customers, only: [ :index, :create, :show, :update ], param: :customer_uuid do
          collection do
            get :search
          end
          member do
            get "driving-license", controller: "/api/v1/driver_license", to: "show"
            post "driving-license", controller: "/api/v1/driver_license", to: "create_or_update"
            delete "driving-license", controller: "/api/v1/driver_license", to: "destroy"
            post "driving-license-image", controller: "/api/v1/driver_license", to: "create_image"
            delete "driving-license-image", controller: "/api/v1/driver_license", to: "destroy_image"
          end
        end
        # Trade plates
        resources :trade_plates, only: :index, path: "trade-plates"
        # Dealership users
        resources :users, only: [ :index ], controller: "dealerships/dealership_users"
        # Drives
        resources :drives, only: [ :index, :create, :show, :destroy ], controller: "dealerships/drives", param: :uuid do
          collection do
            get :dashboard
          end
          member do
            put "start", to: "dealerships/drives#start"
            put "time", to: "dealerships/drives#update_times"
            put "odometer", to: "dealerships/drives#update_odometer"
            put "reassign", to: "dealerships/drives#reassign"
            put "customer", to: "dealerships/drives#update_customer"
            post "damage-report", to: "dealerships/drives#create_damage_report"
            patch "trade-plate", to: "dealerships/drives#assign_trade_plate"
            put "complete", to: "dealerships/drives#complete"
            put "sold-status", to: "dealerships/drives#update_sold_status"
            post "location", to: "dealerships/drives#update_location"
            put "customer-signature", to: "dealerships/drives#attach_customer_signature"
          end
        end
        # Vehicle management
        resources :vehicles, only: [ :create, :index ], controller: "dealerships/vehicles"
        # Appraisal management
        resources :appraisals, only: [ :index, :create, :show, :update, :destroy ], controller: "dealerships/appraisals", param: :appraisal_uuid do
          collection do
            get :dashboard
          end
          member do
            post "vehicle", controller: "dealerships/appraisals", to: "create_vehicle"
            put "vehicle", controller: "dealerships/appraisals", to: "update_vehicle"
            put "customer", controller: "dealerships/appraisals", to: "update_customer"
            put "vehicle-condition", controller: "dealerships/appraisals", to: "upsert_vehicle_condition"
            put "body-part-condition", controller: "dealerships/appraisals", to: "upsert_body_part_condition"
            put "component-rating", controller: "dealerships/appraisals", to: "upsert_component_rating"
            put "reconditioning-costs", controller: "dealerships/appraisals", to: "bulk_upsert_reconditioning_costs"
            put "favourite", controller: "dealerships/appraisals", to: "favourite"
            put "signature", controller: "dealerships/appraisals", to: "attach_customer_signature"
            put "archive", controller: "dealerships/appraisals", to: "archive"
            put "finance-details", controller: "dealerships/appraisals", to: "upsert_finance_details"
            put "vehicle-history", controller: "dealerships/appraisals", to: "upsert_vehicle_history"
            put "options-fitted", controller: "dealerships/appraisals", to: "upsert_options_fitted"
            put "reassign", controller: "dealerships/appraisals", to: "reassign"
            post "send-for-offers", controller: "dealerships/appraisals", to: "send_for_offers"
            get "offers", controller: "dealerships/appraisals", to: "offers"
          end
        end

        # Booking management
        resources :bookings, only: [ :index, :create, :show, :update ], param: :uuid do
          member do
            patch :cancel
            post "create-drive", to: "bookings#create_drive"
          end
        end
        # Enquiry management
        resources :enquiries, only: [ :create ]
        # Appraisal valuers management
        resources :valuers, only: [ :index ], controller: "dealerships/appraisal_valuers"
      end

      # Brands
      resources :brands, only: [ :index ]

      namespace :users do
        namespace :me do
          get "/", controller: "/api/v1/users", to: "profile"
          namespace :documents do
            get "driving-license", controller: "/api/v1/driver_license", to: "show"
            post "driving-license", controller: "/api/v1/driver_license", to: "create_or_update"
            delete "driving-license", controller: "/api/v1/driver_license", to: "destroy"
            post "driving-license-image", controller: "/api/v1/driver_license", to: "create_image"
            delete "driving-license-image", controller: "/api/v1/driver_license", to: "destroy_image"
          end
        end
      end

      resources :user_invitations, only: [ :create ]
    end
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Admin routes
  resources :users do
    resources :device_registrations, only: [ :destroy ]
    resources :user_dealerships, only: [ :create, :destroy ]
  end

  # Autocomplete routes
  get "dealerships/autocomplete", to: "dealerships#autocomplete"

  root "users#index"
end
